<template>
  <div class="dynamic-route-test">
    <n-card title="Hash模式动态路由测试">
      <n-space vertical size="large">
        <!-- 当前路由信息 -->
        <n-alert type="info">
          <template #header>当前路由信息</template>
          <p><strong>路径:</strong> {{ currentRoute.path }}</p>
          <p><strong>Hash:</strong> {{ currentRoute.hash }}</p>
          <p><strong>完整URL:</strong> {{ currentUrl }}</p>
        </n-alert>

        <!-- 动态路由测试 -->
        <n-card title="动态路由测试" size="small">
          <n-space vertical>
            <n-input-group>
              <n-input 
                v-model:value="testRoutePath" 
                placeholder="输入路由路径，如: inventory/orders-page"
                style="width: 300px"
              />
              <n-button type="primary" @click="testDynamicRoute">
                测试路由
              </n-button>
            </n-input-group>
            
            <n-space>
              <n-button @click="testRoutePath = 'inventory/orders-page'">
                库存订单页面
              </n-button>
              <n-button @click="testRoutePath = 'system/users'">
                系统用户页面
              </n-button>
              <n-button @click="testRoutePath = 'customer-page'">
                客户页面
              </n-button>
            </n-space>
          </n-space>
        </n-card>

        <!-- 可用组件列表 -->
        <n-card title="可用组件列表" size="small">
          <n-space vertical>
            <n-button @click="loadAvailableComponents" type="default">
              刷新组件列表
            </n-button>
            
            <n-scrollbar style="max-height: 200px">
              <n-list>
                <n-list-item v-for="component in availableComponents" :key="component">
                  <n-space justify="space-between" style="width: 100%">
                    <span>{{ component }}</span>
                    <n-button 
                      size="small" 
                      @click="navigateToComponent(component)"
                      type="primary"
                      text
                    >
                      访问
                    </n-button>
                  </n-space>
                </n-list-item>
              </n-list>
            </n-scrollbar>
          </n-space>
        </n-card>

        <!-- 路由历史 -->
        <n-card title="路由历史" size="small">
          <n-timeline>
            <n-timeline-item 
              v-for="(item, index) in routeHistory" 
              :key="index"
              :type="item.success ? 'success' : 'error'"
            >
              <template #header>
                {{ item.path }}
              </template>
              <p>{{ item.message }}</p>
              <p><small>{{ item.timestamp }}</small></p>
            </n-timeline-item>
          </n-timeline>
        </n-card>

        <!-- 使用说明 -->
        <n-card title="Hash模式动态路由说明" size="small">
          <n-space vertical>
            <h4>支持的路径格式:</h4>
            <ul>
              <li><code>inventory/orders-page</code> → <code>inventory/OrdersPage.vue</code></li>
              <li><code>system/users</code> → <code>system/UsersPage.vue</code></li>
              <li><code>customer-page</code> → <code>CustomerPage.vue</code></li>
            </ul>
            
            <h4>URL格式:</h4>
            <ul>
              <li>Hash模式: <code>/#/inventory/orders-page</code></li>
              <li>直接访问: 支持</li>
              <li>页面刷新: 支持</li>
              <li>浏览器前进/后退: 支持</li>
            </ul>

            <h4>优势:</h4>
            <ul>
              <li>✅ 无需CDN配置</li>
              <li>✅ 支持动态组件加载</li>
              <li>✅ 完全避免404错误</li>
              <li>✅ 支持路由缓存</li>
            </ul>
          </n-space>
        </n-card>
      </n-space>
    </n-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { 
  NCard, NSpace, NAlert, NInput, NInputGroup, NButton, 
  NList, NListItem, NScrollbar, NTimeline, NTimelineItem 
} from 'naive-ui'
import { dynamicRouteLoader } from '@/router/dynamicRouteLoader.js'

const router = useRouter()
const route = useRoute()

const testRoutePath = ref('')
const availableComponents = ref([])
const routeHistory = ref([])
const currentUrl = ref('')

// 当前路由信息
const currentRoute = computed(() => ({
  path: route.path,
  hash: route.hash,
  fullPath: route.fullPath
}))

// 更新当前URL
const updateCurrentUrl = () => {
  currentUrl.value = window.location.href
}

// 测试动态路由
const testDynamicRoute = async () => {
  if (!testRoutePath.value.trim()) {
    addToHistory(testRoutePath.value, false, '路径不能为空')
    return
  }

  try {
    const success = await dynamicRouteLoader.addDynamicRoute(testRoutePath.value)
    
    if (success) {
      // 导航到新路由
      await router.push(`/${testRoutePath.value}`)
      addToHistory(testRoutePath.value, true, '路由添加并导航成功')
    } else {
      addToHistory(testRoutePath.value, false, '路由添加失败，可能找不到对应组件')
    }
  } catch (error) {
    addToHistory(testRoutePath.value, false, `错误: ${error.message}`)
  }
}

// 导航到组件
const navigateToComponent = async (componentPath) => {
  try {
    const success = await dynamicRouteLoader.addDynamicRoute(componentPath)
    
    if (success) {
      await router.push(`/${componentPath}`)
      addToHistory(componentPath, true, '导航成功')
    } else {
      addToHistory(componentPath, false, '导航失败')
    }
  } catch (error) {
    addToHistory(componentPath, false, `导航错误: ${error.message}`)
  }
}

// 加载可用组件列表
const loadAvailableComponents = () => {
  availableComponents.value = dynamicRouteLoader.getAvailableComponents()
    .filter(path => !path.includes('test/') && !path.includes('system/'))
    .slice(0, 20) // 限制显示数量
}

// 添加到历史记录
const addToHistory = (path, success, message) => {
  routeHistory.value.unshift({
    path,
    success,
    message,
    timestamp: new Date().toLocaleTimeString()
  })
  
  // 限制历史记录数量
  if (routeHistory.value.length > 10) {
    routeHistory.value = routeHistory.value.slice(0, 10)
  }
}

onMounted(() => {
  updateCurrentUrl()
  loadAvailableComponents()
  
  // 监听路由变化
  router.afterEach(() => {
    updateCurrentUrl()
  })
})
</script>

<style scoped>
.dynamic-route-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

ul {
  margin: 0;
  padding-left: 20px;
}

li {
  margin-bottom: 4px;
}

h4 {
  margin: 16px 0 8px 0;
  color: #333;
}
</style>
